#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FCST Processor Module
FCST（預測）資料處理核心模組
用於處理FCST檔案匯入、產品搜尋和報告生成
"""

import os
import csv
import glob
import openpyxl
from datetime import datetime, timedelta
import tempfile
import shutil
import zipfile


class FCSTProcessor:
    """
    FCST處理器核心類別
    提供FCST檔案處理、產品搜尋和報告生成功能
    """
    
    def __init__(self):
        """初始化FCST處理器"""
        pass
    
    # ===================== FCST檔案處理 =====================
    
    def parse_fcst_file(self, fcst_file_path, progress_callback=None):
        """
        解析FCST Excel檔案
        
        Args:
            fcst_file_path: FCST檔案路徑
            progress_callback: 進度回調函數
            
        Returns:
            dict: 包含產品名稱和負責人資訊的字典
        """
        try:
            if progress_callback:
                progress_callback("正在讀取FCST檔案...")

            # 讀取Excel檔案
            workbook = openpyxl.load_workbook(fcst_file_path)
            worksheet = workbook.active

            if progress_callback:
                progress_callback("正在尋找GMT Part No欄位...")

            # 在第一個sheet的A欄中搜尋"GMT Part No"
            gmt_part_no_row = None
            for row in range(1, worksheet.max_row + 1):
                cell_value = worksheet[f'A{row}'].value
                if cell_value and 'GMT Part No' in str(cell_value):
                    gmt_part_no_row = row
                    break

            if gmt_part_no_row is None:
                raise Exception("FCST檔案錯誤：找不到 'GMT Part No' 欄位")

            if progress_callback:
                progress_callback(f"找到GMT Part No在第{gmt_part_no_row}行，正在提取產品清單...")

            # 檢查B欄是否有負責人資訊
            b_header = worksheet[f'B{gmt_part_no_row}'].value
            has_owner_info = b_header and ('負責人' in str(b_header) or 'Owner' in str(b_header) or '工程師' in str(b_header))

            # 從下一行開始提取產品名稱
            products = []
            product_owners = {}
            
            for row in range(gmt_part_no_row + 1, worksheet.max_row + 1):
                part_no_cell = worksheet[f'A{row}'].value
                
                # 如果遇到空格，停止提取
                if not part_no_cell or str(part_no_cell).strip() == '':
                    break
                
                product_name = str(part_no_cell).strip()
                products.append(product_name)
                
                # 如果有負責人欄位，也提取負責人資訊
                if has_owner_info:
                    owner_cell = worksheet[f'B{row}'].value
                    if owner_cell:
                        product_owners[product_name] = str(owner_cell).strip()

            workbook.close()

            if progress_callback:
                progress_callback(f"從FCST檔案中提取到 {len(products)} 個產品")

            return {
                'products': products,
                'product_owners': product_owners,
                'has_owner_info': has_owner_info,
                'source_file': os.path.basename(fcst_file_path)
            }

        except Exception as e:
            raise Exception(f"解析FCST檔案失敗: {str(e)}")
    
    # ===================== 網路路徑搜尋 =====================
    
    def find_valid_paths_with_ft_cp(self, base_path, progress_callback=None):
        """
        找到所有包含FT和CP資料夾的有效路徑
        
        Args:
            base_path: 基礎搜尋路徑
            progress_callback: 進度回調函數
            
        Returns:
            list: 有效路徑列表
        """
        valid_paths = []
        
        # FCST專用的跳過路徑（比一般搜尋多了一些）
        skip_paths = {
            'ASECL', 'ASEKR', 'ASEKS', 'Testserv', 'Truetest',
            'temp', 'backup', 'archive', 'old'
        }
        
        try:
            if not os.path.exists(base_path):
                return valid_paths
                
            for item in os.listdir(base_path):
                # 跳過指定的路徑
                if item in skip_paths:
                    continue
                    
                item_path = os.path.join(base_path, item)
                if not os.path.isdir(item_path):
                    continue
                    
                # 檢查是否同時有FT和CP資料夾
                ft_path = os.path.join(item_path, 'FT')
                cp_path = os.path.join(item_path, 'CP')
                
                if os.path.exists(ft_path) and os.path.exists(cp_path):
                    valid_paths.append(item_path)
                    if progress_callback:
                        progress_callback(f"找到有效路徑: {item}")
                        
        except Exception as e:
            if progress_callback:
                progress_callback(f"搜尋路徑時發生錯誤: {str(e)}")
                
        return valid_paths
    
    def find_matching_product_folders(self, search_path, product_name):
        """
        在指定路徑中找到匹配的產品資料夾
        支援精確匹配和括號變化匹配
        
        Args:
            search_path: 搜尋路徑
            product_name: 產品名稱
            
        Returns:
            list: 匹配的資料夾路徑列表
        """
        matching_folders = []
        
        try:
            if not os.path.exists(search_path):
                return matching_folders
                
            for item in os.listdir(search_path):
                item_path = os.path.join(search_path, item)
                if not os.path.isdir(item_path):
                    continue
                    
                # 精確匹配
                if item == product_name:
                    matching_folders.append(item_path)
                # 括號變化匹配（如 G5409AS1U(AA), G5409AS1U(AB)）
                elif item.startswith(product_name + '(') and item.endswith(')'):
                    matching_folders.append(item_path)
                    
        except Exception as e:
            pass  # 忽略無法存取的資料夾
            
        return matching_folders
    
    def is_folder_in_time_range(self, folder_path, start_date, end_date):
        """
        檢查資料夾是否在指定時間區間內
        
        Args:
            folder_path: 資料夾路徑
            start_date: 開始時間
            end_date: 結束時間
            
        Returns:
            bool: 是否在時間區間內
        """
        try:
            if not os.path.exists(folder_path):
                return False
                
            # 取得資料夾創建時間
            folder_time = os.path.getctime(folder_path)
            folder_date = datetime.fromtimestamp(folder_time)
            
            return start_date <= folder_date <= end_date
            
        except Exception as e:
            return False
    
    def has_valid_subfolders_in_time_range(self, folder_path, start_date, end_date):
        """
        檢查資料夾是否有符合時間區間的子資料夾
        
        Args:
            folder_path: 資料夾路徑
            start_date: 開始時間
            end_date: 結束時間
            
        Returns:
            tuple: (是否有符合條件的子資料夾, 符合條件的子資料夾列表)
        """
        valid_subfolders = []
        
        try:
            if not os.path.exists(folder_path):
                return False, valid_subfolders
                
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    if self.is_folder_in_time_range(item_path, start_date, end_date):
                        valid_subfolders.append(item_path)
                        
        except Exception as e:
            pass
            
        return len(valid_subfolders) > 0, valid_subfolders
    
    def find_zip_files_in_folder(self, folder_path):
        """
        在資料夾中找到所有ZIP檔案
        
        Args:
            folder_path: 資料夾路徑
            
        Returns:
            list: ZIP檔案路徑列表
        """
        zip_files = []
        
        try:
            # 搜尋 *.csv.zip 和 *.spd.zip 檔案
            csv_zip_pattern = os.path.join(folder_path, "*.csv.zip")
            spd_zip_pattern = os.path.join(folder_path, "*.spd.zip")
            
            csv_zip_files = glob.glob(csv_zip_pattern)
            spd_zip_files = glob.glob(spd_zip_pattern)
            
            zip_files.extend(csv_zip_files)
            zip_files.extend(spd_zip_files)
            
        except Exception as e:
            pass
            
        return zip_files
    
    def search_products_in_network(self, fcst_data, start_date, end_date, test_type, 
                                 base_path=r"\\************\test_log", 
                                 progress_callback=None):
        """
        在網路路徑中搜尋FCST產品
        
        Args:
            fcst_data: FCST檔案解析結果
            start_date: 開始時間
            end_date: 結束時間
            test_type: 測試類型（FT或CP）
            base_path: 基礎網路路徑
            progress_callback: 進度回調函數
            
        Returns:
            list: 搜尋結果列表
        """
        results = []
        products = fcst_data['products']
        product_owners = fcst_data.get('product_owners', {})
        
        if progress_callback:
            progress_callback(f"開始搜尋 {test_type} 測試資料...")
            progress_callback(f"時間區間: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}")
        
        try:
            # 找到所有有效路徑
            valid_paths = self.find_valid_paths_with_ft_cp(base_path, progress_callback)
            
            if not valid_paths:
                if progress_callback:
                    progress_callback("未找到任何有效路徑")
                return results
            
            if progress_callback:
                progress_callback(f"找到 {len(valid_paths)} 個有效路徑")
            
            # 遍歷每個產品
            for i, product_name in enumerate(products):
                if progress_callback:
                    progress_callback(f"[{i+1}/{len(products)}] 分析產品: {product_name}")
                
                product_result = {
                    'product_name': product_name,
                    'owner': product_owners.get(product_name, ''),
                    'found_files': [],
                    'search_paths': [],
                    'total_files': 0,
                    'status': 'not_found'
                }
                
                # 在每個有效路徑中搜尋產品
                for valid_path in valid_paths:
                    path_name = os.path.basename(valid_path)
                    test_path = os.path.join(valid_path, test_type)
                    
                    if not os.path.exists(test_path):
                        continue
                    
                    if progress_callback:
                        progress_callback(f"  搜尋路徑: {path_name}\\{test_type}")
                    
                    product_result['search_paths'].append(f"{path_name}\\{test_type}")
                    
                    # 找到匹配的產品資料夾
                    matching_folders = self.find_matching_product_folders(test_path, product_name)
                    
                    for product_folder in matching_folders:
                        # 檢查是否有符合時間區間的子資料夾
                        has_valid, valid_subfolders = self.has_valid_subfolders_in_time_range(
                            product_folder, start_date, end_date
                        )
                        
                        if has_valid:
                            folder_files = 0
                            for subfolder in valid_subfolders:
                                zip_files = self.find_zip_files_in_folder(subfolder)
                                folder_files += len(zip_files)
                                product_result['found_files'].extend(zip_files)
                            
                            if folder_files > 0:
                                if progress_callback:
                                    progress_callback(f"    ✅ 在 {path_name} 中找到 {folder_files} 個檔案")
                                product_result['total_files'] += folder_files
                
                # 設定產品狀態
                if product_result['total_files'] > 0:
                    product_result['status'] = 'found'
                    if progress_callback:
                        progress_callback(f"  型號:{product_name} 總共找到 {product_result['total_files']} 個可分析檔案")
                else:
                    product_result['status'] = 'not_found'
                    if progress_callback:
                        progress_callback(f"  型號:{product_name} 查無符合資料可以比對")
                
                results.append(product_result)
            
            if progress_callback:
                total_files = sum(r['total_files'] for r in results)
                progress_callback(f"✅ FCST分析完成，共處理 {total_files} 個檔案")
            
        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 搜尋過程中發生錯誤: {str(e)}")
        
        return results
    
    # ===================== 報告生成 =====================
    
    def generate_fcst_report(self, search_results, fcst_data, start_date, end_date, 
                           test_type, output_dir=None):
        """
        生成FCST分析報告
        
        Args:
            search_results: 搜尋結果
            fcst_data: FCST檔案資訊
            start_date: 開始時間
            end_date: 結束時間
            test_type: 測試類型
            output_dir: 輸出目錄
            
        Returns:
            tuple: (TXT檔案路徑, CSV檔案路徑)
        """
        # 生成檔案名稱
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = f"FCST_{test_type}_data_check_issues_{timestamp}"
        
        # 確定輸出目錄
        if output_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(script_dir, "..", "..", "CheckReport")
        
        # 創建輸出目錄
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        txt_path = os.path.join(output_dir, f"{base_name}.txt")
        csv_path = os.path.join(output_dir, f"{base_name}.csv")
        
        # 生成TXT報告
        self._save_fcst_txt_report(txt_path, search_results, fcst_data, 
                                 start_date, end_date, test_type)
        
        # 生成CSV報告
        self._save_fcst_csv_report(csv_path, search_results, fcst_data)
        
        return txt_path, csv_path
    
    def _save_fcst_txt_report(self, txt_path, search_results, fcst_data, 
                            start_date, end_date, test_type):
        """保存FCST TXT格式報告"""
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("FCST測試機資料檢查報告\n")
            f.write("=" * 50 + "\n")
            f.write(f"檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"FCST來源檔案: {fcst_data['source_file']}\n")
            f.write(f"測試類型: {test_type}\n")
            f.write(f"時間區間: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"分析產品數量: {len(search_results)}\n")
            f.write("\n")
            
            # 產品清單摘要
            f.write("分析產品清單:\n")
            f.write("-" * 30 + "\n")
            found_count = 0
            for result in search_results:
                status_symbol = "✅" if result['status'] == 'found' else "❌"
                owner_info = f" (負責人: {result['owner']})" if result['owner'] else ""
                f.write(f"  {status_symbol} {result['product_name']}{owner_info} - {result['total_files']} 個檔案\n")
                if result['status'] == 'found':
                    found_count += 1
            
            f.write(f"\n找到資料的產品: {found_count}/{len(search_results)}\n")
            f.write(f"總檔案數量: {sum(r['total_files'] for r in search_results)}\n")
            f.write("\n")
            
            # 詳細搜尋結果
            f.write("詳細搜尋結果:\n")
            f.write("=" * 50 + "\n")
            
            for i, result in enumerate(search_results, 1):
                f.write(f"\n[{i}] 產品: {result['product_name']}\n")
                if result['owner']:
                    f.write(f"負責人: {result['owner']}\n")
                f.write(f"狀態: {'找到資料' if result['status'] == 'found' else '查無符合資料可以比對'}\n")
                f.write(f"檔案數量: {result['total_files']}\n")
                
                if result['search_paths']:
                    f.write("搜尋路徑:\n")
                    for path in result['search_paths']:
                        f.write(f"  - {path}\n")
                
                if result['found_files']:
                    f.write("找到的檔案:\n")
                    for file_path in result['found_files'][:5]:  # 最多顯示5個檔案
                        f.write(f"  - {os.path.basename(file_path)}\n")
                    if len(result['found_files']) > 5:
                        f.write(f"  ... 還有 {len(result['found_files']) - 5} 個檔案\n")
                
                f.write("-" * 30 + "\n")
    
    def _save_fcst_csv_report(self, csv_path, search_results, fcst_data):
        """保存FCST CSV格式報告"""
        with open(csv_path, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            
            # CSV標題
            headers = ['產品名稱', '狀態', '檔案數量', '搜尋路徑', '第一個檔案路徑']
            if fcst_data['has_owner_info']:
                headers.insert(1, '負責人')
            
            writer.writerow(headers)
            
            # CSV數據
            for result in search_results:
                row = [
                    result['product_name'],
                    '找到資料' if result['status'] == 'found' else '查無符合資料可以比對',
                    result['total_files'],
                    '; '.join(result['search_paths'][:3]),  # 最多顯示3個路徑
                    os.path.basename(result['found_files'][0]) if result['found_files'] else ''
                ]
                
                if fcst_data['has_owner_info']:
                    row.insert(1, result['owner'])
                
                writer.writerow(row)


# ===================== 使用範例 =====================

def example_usage():
    """使用範例"""
    # 創建FCST處理器實例
    processor = FCSTProcessor()
    
    def progress_callback(message):
        print(f"進度: {message}")
    
    # 範例1: 解析FCST檔案
    # fcst_data = processor.parse_fcst_file("/path/to/fcst_file.xlsx", progress_callback)
    
    # 範例2: 搜尋產品
    # from datetime import datetime
    # start_date = datetime(2025, 7, 10)
    # end_date = datetime(2025, 7, 15)
    # results = processor.search_products_in_network(
    #     fcst_data, start_date, end_date, "FT", progress_callback=progress_callback
    # )
    
    # 範例3: 生成報告
    # txt_path, csv_path = processor.generate_fcst_report(
    #     results, fcst_data, start_date, end_date, "FT"
    # )
    
    print("FCST處理器模組已準備就緒")


if __name__ == "__main__":
    example_usage()