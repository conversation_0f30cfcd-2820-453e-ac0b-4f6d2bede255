#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TestDataChecker Core Module
核心測試機資料檢查工具模組 - 純邏輯版本
檢查良品（SW_Bin為1或2）是否有缺少資料的問題
"""

import csv
import os
import zipfile
import tempfile
import shutil
import glob
import re
from datetime import datetime, timedelta
from pathlib import Path


class TestDataChecker:
    """
    測試機資料檢查核心類別
    提供資料處理、檔案處理和報告生成功能
    """
    
    def __init__(self):
        """初始化檢查器"""
        self.results = []
    
    # ===================== 檔案處理方法 =====================
    
    def is_supported_file(self, file_path):
        """檢查是否為支援的檔案格式"""
        file_path_lower = file_path.lower()
        return (file_path_lower.endswith('.csv') or
                file_path_lower.endswith('.csv.zip') or
                file_path_lower.endswith('.spd.zip'))
    
    def read_csv_file(self, file_path):
        """讀取CSV檔案"""
        data = []
        with open(file_path, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.reader(f)
            for row in reader:
                data.append(row)
        return data
    
    # ===================== 資料處理方法 =====================
    
    def find_bin_in_e_column(self, data):
        """在E欄位往下找Bin#（新邏輯觸發條件）"""
        e_col_index = 4  # E欄位是索引4 (A=0, B=1, C=2, D=3, E=4)
        
        for row_idx, row in enumerate(data):
            if len(row) > e_col_index:
                cell_value = str(row[e_col_index]).strip()
                if cell_value == "Bin#":
                    return row_idx, e_col_index
        
        return None, None
    
    def find_width_boundary(self, data, bin_row):
        """找到截止範圍(寬度) - 從Bin#往右找到空格的左邊一格"""
        if bin_row >= len(data):
            return None
        
        row_data = data[bin_row]
        
        # 從E欄位往右搜尋
        for col_idx in range(4, len(row_data)):  # 從E欄位(索引4)開始
            cell_value = str(row_data[col_idx]).strip() if col_idx < len(row_data) else ""
            
            # 找到空格
            if cell_value == "":
                width_boundary = col_idx - 1  # 空格的左邊一格
                return width_boundary
        
        # 如果沒找到空格，使用最後一個有資料的欄位
        width_boundary = len(row_data) - 1
        return width_boundary
    
    def find_depth_boundary(self, data, bin_row, bin_col):
        """找到截止範圍(深度) - 從Bin#往下找到空格的上一格"""
        
        # 從Bin#的下一行開始往下搜尋
        for row_idx in range(bin_row + 1, len(data)):
            if bin_col >= len(data[row_idx]):
                depth_boundary = row_idx - 1
                return depth_boundary
            
            cell_value = str(data[row_idx][bin_col]).strip()
            
            # 找到空格
            if cell_value == "":
                depth_boundary = row_idx - 1  # 空格的上一格
                return depth_boundary
        
        # 如果沒找到空格，使用檔案末尾
        depth_boundary = len(data) - 1
        return depth_boundary
    
    def check_new_csv_logic(self, data, progress_callback=None):
        """執行新的CSV檢查邏輯（E欄位Bin#格式）"""
        issues = []
        
        if progress_callback:
            progress_callback("使用新邏輯：E欄位Bin#格式檢查...")
        
        # 1. 找到E欄位的Bin#
        bin_row, bin_col = self.find_bin_in_e_column(data)
        if bin_row is None:
            return issues, 0  # 沒找到Bin#，不適用新邏輯
        
        if progress_callback:
            progress_callback(f"找到 Bin# 在 E{bin_row+1}")
        
        # 2. 找到截止範圍(寬度)
        width_boundary = self.find_width_boundary(data, bin_row)
        if width_boundary is None:
            return issues, 0
        
        if progress_callback:
            progress_callback(f"截止範圍(寬度): {self.col_index_to_excel(width_boundary)}")
        
        # 3. 找到截止範圍(深度)  
        depth_boundary = self.find_depth_boundary(data, bin_row, bin_col)
        if depth_boundary is None:
            return issues, 0
        
        if progress_callback:
            progress_callback(f"截止範圍(深度): E{depth_boundary+1}")
        
        # 4. 檢查每一行的資料
        good_products_count = 0
        
        for row_idx in range(bin_row + 1, depth_boundary + 1):
            if row_idx >= len(data):
                break
                
            row_data = data[row_idx]
            
            # 取得E欄位的Bin值
            if bin_col >= len(row_data):
                continue
            
            try:
                bin_value = float(str(row_data[bin_col]).strip())
            except (ValueError, IndexError):
                continue  # 跳過無效的Bin值
            
            # 取得右邊界欄位的值
            if width_boundary >= len(row_data):
                right_boundary_value = ""
            else:
                right_boundary_value = str(row_data[width_boundary]).strip()
            
            # 判斷邏輯：良品(≤4)且右邊界以NA開頭 → 異常
            if bin_value <= 4:
                good_products_count += 1
                
                if str(right_boundary_value).upper().startswith("NA"):
                    issues.append({
                        'row': row_idx + 1,  # Excel行號
                        'bin_value': bin_value,
                        'right_boundary_col': self.col_index_to_excel(width_boundary),
                        'right_boundary_value': right_boundary_value,
                        'missing_columns': f"{self.col_index_to_excel(width_boundary)}{row_idx+1}",
                        'description': f"良品(Bin={bin_value})在{self.col_index_to_excel(width_boundary)}{row_idx+1}位置測試資料異常"
                    })
        
        if progress_callback:
            progress_callback(f"新邏輯檢查完成: 良品數量={good_products_count}, 異常數量={len(issues)}")
        
        return issues, good_products_count
    
    def find_data_section(self, data):
        """找到[Data]區段的位置"""
        for row_idx, row in enumerate(data):
            for col_idx, cell in enumerate(row):
                if cell and str(cell).strip() == '[Data]':
                    return row_idx
        return None
    
    def find_qa_data_section(self, data):
        """找到[QAData]區段的位置"""
        for row_idx, row in enumerate(data):
            for col_idx, cell in enumerate(row):
                if cell and str(cell).strip() == '[QAData]':
                    return row_idx
        return None
    
    def find_sw_bin_column(self, data, data_start_row):
        """找到SW_Bin欄位的位置"""
        if data_start_row is None:
            return None, None
            
        # 從[Data]往下1格後往右搜尋SW_Bin
        search_row = data_start_row + 1
        if search_row >= len(data):
            return None, None
            
        for col_idx in range(len(data[search_row])):
            cell_value = data[search_row][col_idx] if col_idx < len(data[search_row]) else ""
            if cell_value and 'SW_Bin' in str(cell_value):
                return search_row, col_idx
        return None, None
    
    def check_good_products_data(self, data, sw_bin_row, sw_bin_col, progress_callback=None):
        """檢查良品資料是否完整（優化版本）"""
        issues = []

        if sw_bin_row is None or sw_bin_col is None:
            return issues, 0

        # 從SW_Bin往下4格開始檢查產品數據
        data_start_row = sw_bin_row + 4

        # 找到[QAData]區段的位置，如果存在則只分析到該位置之前
        qa_data_row = self.find_qa_data_section(data)
        if qa_data_row is not None:
            data_end_row = qa_data_row
        else:
            data_end_row = len(data)

        # 找出最大列數
        max_cols = max(len(row) for row in data) if data else 0

        total_rows = data_end_row - data_start_row
        if progress_callback:
            progress_callback(f"正在分析 {total_rows} 行產品資料...")

        # 第一步：快速收集所有良品資料（優化：一次掃描）
        good_products = []
        for i, row_idx in enumerate(range(data_start_row, data_end_row)):
            if progress_callback and i % 50 == 0:  # 減少進度更新頻率
                progress = (i / total_rows) * 30  # 這階段佔30%
                progress_callback(f"收集良品資料: {progress:.1f}%")

            if row_idx >= len(data) or sw_bin_col >= len(data[row_idx]):
                continue

            sw_bin_value = data[row_idx][sw_bin_col] if sw_bin_col < len(data[row_idx]) else ""

            # 如果SW_Bin為1或2（良品）
            if sw_bin_value and str(sw_bin_value).strip() in ['1', '2']:
                current_row = data[row_idx] if row_idx < len(data) else []
                # 預處理：找到最後一個非空格子
                last_non_empty_col = None
                for col_idx in range(len(current_row) - 1, -1, -1):
                    cell_value = current_row[col_idx] if col_idx < len(current_row) else ""
                    if cell_value and str(cell_value).strip() != '':
                        last_non_empty_col = col_idx
                        break

                good_products.append({
                    'row_idx': row_idx,
                    'sw_bin': sw_bin_value,
                    'data': current_row,
                    'last_non_empty_col': last_non_empty_col
                })

        if progress_callback:
            progress_callback(f"找到 {len(good_products)} 個良品，開始比對分析...")

        # 第二步：建立列位置的資料存在映射（優化：避免重複掃描）
        col_data_map = {}
        for col_idx in range(max_cols):
            col_data_map[col_idx] = set()
            for product in good_products:
                if col_idx < len(product['data']):
                    cell_value = product['data'][col_idx]
                    if cell_value and str(cell_value).strip() != '':
                        col_data_map[col_idx].add(product['row_idx'])

        if progress_callback:
            progress_callback("正在進行快速比對分析...")

        # 第三步：快速比對檢查（優化：使用預建的映射）
        for i, product in enumerate(good_products):
            if progress_callback and i % 20 == 0:  # 減少進度更新頻率
                progress = 30 + (i / len(good_products)) * 70  # 這階段佔70%
                progress_callback(f"比對進度: {progress:.1f}% ({i}/{len(good_products)})")

            row_idx = product['row_idx']
            sw_bin_value = product['sw_bin']
            current_row = product['data']
            last_non_empty_col = product['last_non_empty_col']
            
            # 檢查從最後一個非空格子到最右邊是否有空格
            if last_non_empty_col is not None:
                # 使用預建的映射快速檢查（優化：避免嵌套迴圈）
                has_missing_data = False
                missing_range = []

                # 檢查該行右邊的列是否有其他良品有資料
                for col_idx in range(last_non_empty_col + 1, max_cols):
                    # 快速檢查：這個列位置是否有其他良品有資料
                    if col_idx in col_data_map and len(col_data_map[col_idx]) > 0:
                        # 檢查當前良品在這個位置是否有資料
                        current_value = current_row[col_idx] if col_idx < len(current_row) else ""
                        if not current_value or str(current_value).strip() == '':
                            # 確認其他良品確實在這個位置有資料
                            if row_idx not in col_data_map[col_idx]:
                                has_missing_data = True
                                if not missing_range:
                                    missing_range = [col_idx, col_idx]
                                else:
                                    missing_range[1] = col_idx

                if has_missing_data:
                    # 轉換列索引為Excel格式
                    start_col = self.col_index_to_excel(missing_range[0])
                    end_col = self.col_index_to_excel(missing_range[1])
                    row_excel = row_idx + 1  # Excel行號從1開始

                    if start_col == end_col:
                        missing_cols = start_col
                    else:
                        missing_cols = f"{start_col}~{end_col}"

                    issues.append({
                        'row': row_excel,
                        'sw_bin': sw_bin_value,
                        'missing_columns': f"{missing_cols}{row_excel}",
                        'description': f"良品在{missing_cols}{row_excel}位置缺少資料"
                    })

        if progress_callback:
            progress_callback("比對分析完成！")

        return issues, len(good_products)
    
    def check_spd_good_products_data(self, data, bin_row, bin_col, file_path, progress_callback=None):
        """檢查SPD格式良品資料是否完整（通用邏輯）"""
        issues = []

        if progress_callback:
            progress_callback("正在分析SPD檔案結構...")

        # 第7行（索引6）是測試項目標題行
        test_header_row = 6  # A7 對應索引6

        if test_header_row >= len(data):
            return issues, 0

        # 從A7開始往右找1.01.01確定開始點
        start_col = None
        end_col = None
        header_row_data = data[test_header_row]

        # 找到X.01.01的位置（X可以是任何數字）
        for col_idx in range(len(header_row_data)):
            cell_value = header_row_data[col_idx].strip() if col_idx < len(header_row_data) else ""
            if re.match(r'^\d+\.01\.01$', cell_value):  # 匹配 數字.01.01 的模式
                start_col = col_idx
                if progress_callback:
                    progress_callback(f"找到測試項目起始點: {cell_value} 在 {self.col_index_to_excel(col_idx)}7")
                break

        if start_col is None:
            if progress_callback:
                progress_callback("找不到測試項目起始點(X.01.01)")
            return issues, 0

        # 從起始點往右找到第一個空格確定結束點
        for col_idx in range(start_col, len(header_row_data)):
            cell_value = header_row_data[col_idx].strip() if col_idx < len(header_row_data) else ""
            if cell_value == "":
                end_col = col_idx - 1  # 空格的左邊是結束點
                break

        if end_col is None:
            end_col = len(header_row_data) - 1

        start_col_name = self.col_index_to_excel(start_col)
        end_col_name = self.col_index_to_excel(end_col)

        if progress_callback:
            progress_callback(f"測試項目範圍: {start_col_name}7~{end_col_name}7 (第{start_col+1}列到第{end_col+1}列)")

        # 從Bin#行的下一行開始檢查資料
        data_start_row = bin_row + 1

        # 收集所有良品資料（Bin# = 1 或 2）
        good_products = []
        for row_idx in range(data_start_row, len(data)):
            row = data[row_idx]
            if len(row) > bin_col:
                try:
                    bin_value = int(row[bin_col]) if row[bin_col].strip() else None
                    if bin_value in [1, 2]:
                        good_products.append({
                            'row_idx': row_idx,
                            'serial': row[0] if len(row) > 0 else '',
                            'bin': bin_value,
                            'data': row
                        })
                except (ValueError, IndexError):
                    continue

        if not good_products:
            return issues, 0

        if progress_callback:
            progress_callback(f"找到 {len(good_products)} 個良品，正在檢查資料完整性...")

        # 檢查每個良品的資料完整性
        for i, product in enumerate(good_products):
            if progress_callback and i % 10 == 0:
                progress_callback(f"檢查良品 {i+1}/{len(good_products)}")

            row_data = product['data']
            row_idx = product['row_idx']
            serial = product['serial']
            bin_value = product['bin']

            # 找到該行的實際資料結束點（從右往左找第一個非空非0的欄位）
            actual_end_col = None
            for col_idx in range(end_col, start_col - 1, -1):  # 從右往左搜尋
                cell_value = row_data[col_idx].strip() if col_idx < len(row_data) else ""
                if cell_value != "" and cell_value != "0":
                    actual_end_col = col_idx
                    break

            if actual_end_col is None:
                continue  # 該行沒有有效資料

            # 檢查從實際結束點到測試範圍結束點之間是否有空格
            for col_idx in range(actual_end_col + 1, end_col + 1):
                cell_value = row_data[col_idx].strip() if col_idx < len(row_data) else ""

                # 在應該有資料的範圍內發現空格，這是異常
                if cell_value == "":
                    col_name = self.col_index_to_excel(col_idx)
                    excel_row = row_idx + 1

                    issues.append({
                        'row': excel_row,
                        'column': col_name,
                        'serial': serial,
                        'bin': bin_value,
                        'header': f'{col_name}{excel_row}',
                        'issue': f"良品(Bin#{bin_value})在{col_name}{excel_row}儲存格缺少資料",
                        'value': '空格'
                    })

                # 如果是0，需要檢查左邊的欄位是否也是0
                elif cell_value == "0":
                    # 檢查左邊的欄位
                    left_col_idx = col_idx - 1
                    if left_col_idx >= 0:
                        left_cell_value = row_data[left_col_idx].strip() if left_col_idx < len(row_data) else ""

                        # 只有當前欄位和左邊欄位都是0時才算異常
                        if left_cell_value == "0":
                            col_name = self.col_index_to_excel(col_idx)
                            excel_row = row_idx + 1

                            issues.append({
                                'row': excel_row,
                                'column': col_name,
                                'serial': serial,
                                'bin': bin_value,
                                'header': f'{col_name}{excel_row}',
                                'issue': f"良品(Bin#{bin_value})在{col_name}{excel_row}儲存格缺少資料",
                                'value': cell_value
                            })

        if progress_callback:
            progress_callback("SPD比對分析完成！")

        return issues, len(good_products)
    
    # ===================== 工具方法 =====================
    
    def col_index_to_excel(self, col_idx):
        """將列索引轉換為Excel格式（A, B, C, ..., AA, AB, ...）"""
        result = ""
        while col_idx >= 0:
            result = chr(col_idx % 26 + ord('A')) + result
            col_idx = col_idx // 26 - 1
        return result

    def excel_col_to_index(self, col_name):
        """將Excel欄位名稱轉換為索引 (0-based)"""
        result = 0
        for char in col_name:
            result = result * 26 + (ord(char.upper()) - ord('A') + 1)
        return result - 1
    
    # ===================== 檔案檢查方法 =====================
    
    def check_file(self, file_path, progress_callback=None):
        """檢查單個檔案（自動選擇檢查邏輯）"""
        try:
            if progress_callback:
                progress_callback("正在讀取檔案...")

            # 讀取CSV檔案
            data = self.read_csv_file(file_path)

            if progress_callback:
                progress_callback(f"已讀取 {len(data)} 行資料，正在判斷檔案格式...")
            
            # 1. 先嘗試新邏輯：檢查E欄位是否有Bin#
            bin_row, bin_col = self.find_bin_in_e_column(data)
            
            if bin_row is not None:
                # 使用新邏輯（E欄位Bin#格式）
                if progress_callback:
                    progress_callback("檢測到E欄位Bin#格式，使用新邏輯檢查...")
                
                issues, total_good_products = self.check_new_csv_logic(data, progress_callback)
                
                return {
                    'file': os.path.basename(file_path),
                    'status': 'success',
                    'message': f'檢查完成，發現 {len(issues)} 個問題（新邏輯）',
                    'issues': issues,
                    'total_good_products': total_good_products,
                    'logic_type': 'new_e_column_bin',
                    'bin_location': f"E{bin_row + 1}"
                }
            
            # 2. 如果沒找到E欄位Bin#，使用原有邏輯
            if progress_callback:
                progress_callback("未檢測到E欄位Bin#，使用原有邏輯檢查...")
                progress_callback("正在尋找[Data]區段...")

            # 找到[Data]區段
            data_section_row = self.find_data_section(data)
            if data_section_row is None:
                return {
                    'file': os.path.basename(file_path),
                    'status': 'error',
                    'message': '找不到[Data]區段',
                    'issues': []
                }

            if progress_callback:
                progress_callback("正在尋找SW_Bin欄位...")

            # 找到SW_Bin欄位
            sw_bin_row, sw_bin_col = self.find_sw_bin_column(data, data_section_row)
            if sw_bin_row is None:
                return {
                    'file': os.path.basename(file_path),
                    'status': 'error',
                    'message': '找不到SW_Bin欄位',
                    'issues': []
                }
            
            if progress_callback:
                progress_callback("正在檢查QAData區段...")

            # 檢查是否有QAData區段
            qa_data_row = self.find_qa_data_section(data)
            qa_data_info = ""
            if qa_data_row is not None:
                qa_data_info = f"，在第{qa_data_row + 1}行發現[QAData]，已停止分析"

            if progress_callback:
                progress_callback("正在檢查良品資料完整性...")

            # 檢查良品資料
            issues, total_good_products = self.check_good_products_data(data, sw_bin_row, sw_bin_col, progress_callback)

            return {
                'file': os.path.basename(file_path),
                'status': 'success',
                'message': f'檢查完成，發現 {len(issues)} 個問題{qa_data_info}（原有邏輯）',
                'issues': issues,
                'total_good_products': total_good_products,
                'logic_type': 'original_data_section',
                'sw_bin_location': f"第{sw_bin_row + 1}行第{sw_bin_col + 1}列",
                'qa_data_location': f"第{qa_data_row + 1}行" if qa_data_row is not None else None
            }
            
        except Exception as e:
            return {
                'file': os.path.basename(file_path),
                'status': 'error',
                'message': f'檢查失敗: {str(e)}',
                'issues': []
            }
    
    def check_spd_csv_file(self, file_path, progress_callback=None):
        """檢查 SPD 格式的 CSV 檔案"""
        try:
            if progress_callback:
                progress_callback("正在讀取SPD檔案...")

            # 讀取CSV檔案
            data = self.read_csv_file(file_path)

            if progress_callback:
                progress_callback(f"已讀取 {len(data)} 行資料，正在分析...")

            # 在B欄位搜尋 Bin#
            bin_row = None
            bin_col = 1  # B欄位是索引1

            for row_idx, row in enumerate(data):
                if len(row) > bin_col and row[bin_col] == 'Bin#':
                    bin_row = row_idx
                    break

            if bin_row is None:
                return {
                    'file': os.path.basename(file_path),
                    'status': 'error',
                    'message': '在B欄位找不到 Bin# 標題',
                    'issues': []
                }

            if progress_callback:
                progress_callback(f"找到 Bin# 在第 {bin_row + 1} 行，正在檢查良品資料...")

            # 檢查良品資料
            issues, total_good_products = self.check_spd_good_products_data(data, bin_row, bin_col, file_path, progress_callback)

            if progress_callback:
                progress_callback("SPD檔案檢查完成！")

            return {
                'file': os.path.basename(file_path),
                'status': 'success',
                'message': f'檢查完成，發現 {len(issues)} 個問題',
                'issues': issues,
                'total_good_products': total_good_products
            }

        except Exception as e:
            return {
                'file': os.path.basename(file_path),
                'status': 'error',
                'message': f'檢查檔案時發生錯誤: {str(e)}',
                'issues': []
            }
    
    def extract_and_check_zip_file(self, zip_path, progress_callback=None):
        """解壓縮並檢查 zip 檔案（支援 csv.zip 和 spd.zip）"""
        temp_dir = None
        try:
            if progress_callback:
                progress_callback(f"正在解壓縮: {os.path.basename(zip_path)}")

            # 建立臨時目錄
            temp_dir = tempfile.mkdtemp()

            # 解壓縮檔案
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)

            # 檢查是否為 spd.zip 檔案
            is_spd_zip = zip_path.lower().endswith('.spd.zip')

            # 找到目標檔案
            target_file = None
            if is_spd_zip:
                # 尋找 .spd 檔案並重新命名為 .csv
                spd_files = glob.glob(os.path.join(temp_dir, "*.spd"))
                if not spd_files:
                    raise Exception("SPD.ZIP檔案中沒有找到SPD檔案")

                spd_file = spd_files[0]
                # 將 .spd 重新命名為 .csv
                csv_file = spd_file.replace('.spd', '.csv')
                os.rename(spd_file, csv_file)
                target_file = csv_file
            else:
                # 尋找 .csv 檔案
                csv_files = glob.glob(os.path.join(temp_dir, "*.csv"))
                if not csv_files:
                    raise Exception("ZIP檔案中沒有找到CSV檔案")
                target_file = csv_files[0]

            if progress_callback:
                progress_callback(f"正在檢查: {os.path.basename(target_file)}")

            # 根據檔案類型選擇檢查方法
            if is_spd_zip:
                result = self.check_spd_csv_file(target_file, progress_callback)
            else:
                result = self.check_file(target_file, progress_callback)

            # 更新結果中的檔案名稱為原始ZIP檔案名稱
            result['file'] = os.path.basename(zip_path)
            result['original_zip_path'] = zip_path

            return result

        except Exception as e:
            return {
                'file': os.path.basename(zip_path),
                'status': 'error',
                'message': f'處理ZIP檔案失敗: {str(e)}',
                'issues': [],
                'original_zip_path': zip_path
            }
        finally:
            # 清理臨時目錄
            if temp_dir and os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
    
    # ===================== 資料夾分析方法 =====================
    
    def analyze_folder(self, folder_path, progress_callback=None):
        """分析指定資料夾中的所有檔案"""
        found_files = []

        try:
            if progress_callback:
                progress_callback("正在搜尋資料夾中的檔案...")

            # 檢查資料夾是否存在
            if not os.path.exists(folder_path):
                raise Exception(f"資料夾不存在: {folder_path}")

            if not os.path.isdir(folder_path):
                raise Exception(f"指定路徑不是資料夾: {folder_path}")

            # 搜尋所有 *.csv 和 *.csv.zip 檔案
            csv_files = glob.glob(os.path.join(folder_path, "*.csv"))
            zip_files = glob.glob(os.path.join(folder_path, "*.csv.zip"))

            all_files = csv_files + zip_files

            if progress_callback:
                progress_callback(f"找到 {len(all_files)} 個檔案 (CSV: {len(csv_files)}, ZIP: {len(zip_files)})")

            return all_files

        except Exception as e:
            if progress_callback:
                progress_callback(f"搜尋失敗: {str(e)}")
            raise e
    
    def search_network_files(self, base_path, test_type, start_date, end_date, progress_callback=None):
        """搜尋網路路徑下符合條件的檔案"""
        found_files = []

        try:
            if progress_callback:
                progress_callback("正在搜尋網路路徑...")

            # 檢查網路路徑是否存在
            if not os.path.exists(base_path):
                raise Exception(f"無法存取網路路徑: {base_path}")

            # 遍歷所有產品資料夾（跳過指定路徑）
            # 全域要跳過的路徑
            skip_paths = {'ASECL', 'ASEKR', 'ASEKS', 'Testserv', 'Truetest'}

            product_folders = []
            for item in os.listdir(base_path):
                # 跳過指定的路徑
                if item in skip_paths:
                    continue

                item_path = os.path.join(base_path, item)
                if os.path.isdir(item_path):
                    product_folders.append(item_path)

            if progress_callback:
                progress_callback(f"找到 {len(product_folders)} 個產品資料夾")

            total_folders = len(product_folders)
            for i, product_folder in enumerate(product_folders):
                if progress_callback:
                    progress = (i / total_folders) * 50  # 搜尋階段佔50%
                    progress_callback(f"搜尋進度: {progress:.1f}% - {os.path.basename(product_folder)}")

                # 檢查是否有對應的測試類型資料夾
                test_type_path = os.path.join(product_folder, test_type)
                if not os.path.exists(test_type_path):
                    continue

                # 遍歷產品型號資料夾
                for model_folder in os.listdir(test_type_path):
                    model_path = os.path.join(test_type_path, model_folder)
                    if not os.path.isdir(model_path):
                        continue

                    # 遍歷子資料夾
                    for sub_folder in os.listdir(model_path):
                        sub_path = os.path.join(model_path, sub_folder)
                        if not os.path.isdir(sub_path):
                            continue

                        # 搜尋符合條件的 *.csv.zip 檔案
                        zip_files = self.find_matching_zip_files(sub_path, start_date, end_date)
                        found_files.extend(zip_files)

            if progress_callback:
                progress_callback(f"搜尋完成，找到 {len(found_files)} 個符合條件的檔案")

            return found_files

        except Exception as e:
            if progress_callback:
                progress_callback(f"搜尋失敗: {str(e)}")
            raise e

    def find_matching_zip_files(self, folder_path, start_date, end_date):
        """在指定資料夾中找到符合時間條件的 *.csv.zip 和 *.spd.zip 檔案"""
        matching_files = []

        try:
            # 搜尋所有 *.csv.zip 和 *.spd.zip 檔案
            csv_zip_pattern = os.path.join(folder_path, "*.csv.zip")
            spd_zip_pattern = os.path.join(folder_path, "*.spd.zip")

            csv_zip_files = glob.glob(csv_zip_pattern)
            spd_zip_files = glob.glob(spd_zip_pattern)

            # 合併所有zip檔案
            all_zip_files = csv_zip_files + spd_zip_files

            # 按時間篩選檔案
            files_in_range = []
            for zip_file in all_zip_files:
                file_time = os.path.getctime(zip_file)
                file_date = datetime.fromtimestamp(file_time)

                if start_date <= file_date <= end_date:
                    files_in_range.append((zip_file, os.path.getsize(zip_file)))

            # 如果有多個檔案，選擇最大的
            if files_in_range:
                largest_file = max(files_in_range, key=lambda x: x[1])
                matching_files.append(largest_file[0])

        except Exception as e:
            # 忽略無法存取的資料夾
            pass

        return matching_files
    
    # ===================== 報告生成方法 =====================
    
    def generate_output_filename(self, file_paths):
        """生成輸出檔案名稱"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if len(file_paths) == 1:
            # 單一檔案：取第一個底線前的部分
            filename = os.path.basename(file_paths[0])
            if '_' in filename:
                prefix = filename.split('_')[0]
            else:
                prefix = os.path.splitext(filename)[0]
            base_name = f"{prefix}_data_check_issues_{timestamp}"
        else:
            # 多個檔案：使用"多產品分析"
            base_name = f"多產品分析_data_check_issues_{timestamp}"

        return base_name
    
    def save_csv_report(self, csv_path, results):
        """保存CSV格式的報告"""
        # 保存CSV格式的問題清單（無論是否有問題都要保存）
        csv_data = []
        for result in results:
            file_issue_count = len(result['issues']) if result['issues'] else 0
            # 計算總分析數量（良品數量）
            total_analyzed = result.get('total_good_products', 0)

            if result['issues']:
                for issue in result['issues']:
                    # 處理不同格式的問題報告
                    if 'sw_bin' in issue:
                        # 原始 CSV 格式
                        csv_data.append([
                            result['file'],
                            issue['row'],
                            issue['sw_bin'],
                            issue['missing_columns'],
                            file_issue_count,
                            total_analyzed,
                            issue['description']
                        ])
                    elif 'bin_value' in issue:
                        # 新邏輯格式（E欄位Bin#）
                        csv_data.append([
                            result['file'],
                            issue['row'],
                            f"Bin={issue['bin_value']}",
                            issue['missing_columns'],
                            file_issue_count,
                            total_analyzed,
                            issue['description']
                        ])
                    elif 'bin' in issue:
                        # SPD 格式
                        csv_data.append([
                            result['file'],
                            issue['row'],
                            f"Bin#{issue['bin']}",
                            f"{issue['column']}({issue['header']})",
                            file_issue_count,
                            total_analyzed,
                            issue['issue']
                        ])
                    else:
                        # 通用格式
                        description = issue.get('issue') or issue.get('description') or '未知問題'
                        csv_data.append([
                            result['file'],
                            issue.get('row', '-'),
                            '-',
                            '-',
                            file_issue_count,
                            total_analyzed,
                            description
                        ])
            else:
                # 即使沒有問題也要記錄
                csv_data.append([
                    result['file'],
                    '-',
                    '-',
                    '-',
                    0,
                    total_analyzed,
                    '未發現問題'
                ])

        # 總是保存CSV檔案
        with open(csv_path, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['檔案名稱', '問題行號', 'SW_Bin值', '缺少資料位置', '問題datalog數量', '總分析數量', '問題描述'])
            writer.writerows(csv_data)
    
    def save_txt_report(self, txt_path, results):
        """保存TXT格式的詳細報告"""
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("測試機資料檢查報告\n")
            f.write("=" * 50 + "\n")
            f.write(f"檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for result in results:
                f.write(f"檔案: {result['file']}\n")
                f.write(f"狀態: {result['status']}\n")
                f.write(f"訊息: {result['message']}\n")
                
                if 'sw_bin_location' in result:
                    f.write(f"SW_Bin位置: {result['sw_bin_location']}\n")

                if 'qa_data_location' in result and result['qa_data_location']:
                    f.write(f"QAData位置: {result['qa_data_location']}（已停止分析）\n")

                if result['issues']:
                    f.write("發現的問題:\n")
                    for issue in result['issues']:
                        # 處理不同格式的問題報告
                        if 'sw_bin' in issue:
                            # 原始 CSV 格式
                            f.write(f"  - 第{issue['row']}行 (SW_Bin={issue['sw_bin']}): {issue['description']}\n")
                        elif 'bin_value' in issue:
                            # 新邏輯格式（E欄位Bin#）
                            f.write(f"  - 第{issue['row']}行 (Bin={issue['bin_value']}): {issue['description']}\n")
                        elif 'bin' in issue:
                            # SPD 格式
                            f.write(f"  - 第{issue['row']}行 欄{issue['column']} (Serial:{issue['serial']} Bin:{issue['bin']}): {issue['issue']}\n")
                        else:
                            # 通用格式
                            description = issue.get('issue') or issue.get('description') or '未知問題'
                            f.write(f"  - 第{issue['row']}行: {description}\n")
                else:
                    f.write("未發現問題\n")
                
                f.write("-" * 30 + "\n\n")
    
    def save_results(self, results, file_paths, output_dir=None):
        """保存檢查結果"""
        base_name = self.generate_output_filename(file_paths)

        # 確定輸出目錄
        if output_dir is None:
            # 預設為程式同路徑下的 CheckReport 資料夾
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(script_dir, "CheckReport")

        # 如果 CheckReport 資料夾不存在，創建它
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        csv_path = os.path.join(output_dir, f"{base_name}.csv")
        txt_path = os.path.join(output_dir, f"{base_name}.txt")

        # 保存詳細報告（TXT格式）
        self.save_txt_report(txt_path, results)
        
        # 保存CSV格式的問題清單
        self.save_csv_report(csv_path, results)

        return txt_path, csv_path


# ===================== 使用範例 =====================

def example_usage():
    """使用範例"""
    # 創建檢查器實例
    checker = TestDataChecker()
    
    # 檢查單個檔案
    def progress_callback(message):
        print(f"進度: {message}")
    
    # 範例1: 檢查CSV檔案
    # result = checker.check_file("/path/to/your/file.csv", progress_callback)
    
    # 範例2: 檢查ZIP檔案
    # result = checker.extract_and_check_zip_file("/path/to/your/file.csv.zip", progress_callback)
    
    # 範例3: 分析資料夾
    # files = checker.analyze_folder("/path/to/your/folder", progress_callback)
    
    # 範例4: 保存結果
    # results = [result]  # 結果列表
    # file_paths = ["/path/to/your/file.csv"]  # 檔案路徑列表
    # txt_path, csv_path = checker.save_results(results, file_paths)
    
    print("TestDataChecker 核心模組已準備就緒")


if __name__ == "__main__":
    example_usage()