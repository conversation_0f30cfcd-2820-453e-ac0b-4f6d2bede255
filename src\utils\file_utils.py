#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Utilities Module
檔案處理工具模組
提供通用的檔案操作功能
"""

import os
import glob
import zipfile
import tempfile
import shutil
from datetime import datetime
from pathlib import Path


class FileUtils:
    """檔案處理工具類"""
    
    @staticmethod
    def is_supported_file(file_path):
        """
        檢查是否為支援的檔案格式
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            bool: 是否為支援的格式
        """
        file_path_lower = file_path.lower()
        return (file_path_lower.endswith('.csv') or
                file_path_lower.endswith('.csv.zip') or
                file_path_lower.endswith('.spd.zip'))
    
    @staticmethod
    def get_file_extension(file_path):
        """
        取得檔案副檔名
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            str: 檔案副檔名（含點）
        """
        return os.path.splitext(file_path)[1].lower()
    
    @staticmethod
    def get_file_size(file_path):
        """
        取得檔案大小
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            int: 檔案大小（bytes）
        """
        try:
            return os.path.getsize(file_path)
        except:
            return 0
    
    @staticmethod
    def get_file_modified_time(file_path):
        """
        取得檔案修改時間
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            datetime: 檔案修改時間
        """
        try:
            timestamp = os.path.getmtime(file_path)
            return datetime.fromtimestamp(timestamp)
        except:
            return None
    
    @staticmethod
    def get_file_created_time(file_path):
        """
        取得檔案創建時間
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            datetime: 檔案創建時間
        """
        try:
            timestamp = os.path.getctime(file_path)
            return datetime.fromtimestamp(timestamp)
        except:
            return None
    
    @staticmethod
    def ensure_directory_exists(directory_path):
        """
        確保目錄存在，如果不存在則創建
        
        Args:
            directory_path: 目錄路徑
            
        Returns:
            bool: 是否成功創建或已存在
        """
        try:
            if not os.path.exists(directory_path):
                os.makedirs(directory_path)
            return True
        except:
            return False
    
    @staticmethod
    def find_files_by_pattern(directory, pattern):
        """
        根據模式搜尋檔案
        
        Args:
            directory: 搜尋目錄
            pattern: 檔案模式（如 *.csv）
            
        Returns:
            list: 符合模式的檔案路徑列表
        """
        try:
            search_pattern = os.path.join(directory, pattern)
            return glob.glob(search_pattern)
        except:
            return []
    
    @staticmethod
    def find_files_by_extensions(directory, extensions, recursive=False):
        """
        根據副檔名搜尋檔案
        
        Args:
            directory: 搜尋目錄
            extensions: 副檔名列表（如 ['.csv', '.zip']）
            recursive: 是否遞迴搜尋子目錄
            
        Returns:
            list: 符合條件的檔案路徑列表
        """
        found_files = []
        
        try:
            if recursive:
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_ext = os.path.splitext(file)[1].lower()
                        if file_ext in extensions:
                            found_files.append(os.path.join(root, file))
            else:
                for file in os.listdir(directory):
                    file_path = os.path.join(directory, file)
                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(file)[1].lower()
                        if file_ext in extensions:
                            found_files.append(file_path)
        except:
            pass
            
        return found_files
    
    @staticmethod
    def extract_zip_file(zip_path, extract_to=None):
        """
        解壓縮ZIP檔案
        
        Args:
            zip_path: ZIP檔案路徑
            extract_to: 解壓縮目標目錄，如果為None則使用臨時目錄
            
        Returns:
            str: 解壓縮後的目錄路徑
        """
        if extract_to is None:
            extract_to = tempfile.mkdtemp()
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            return extract_to
        except Exception as e:
            # 如果是臨時目錄且解壓縮失敗，清理目錄
            if extract_to.startswith(tempfile.gettempdir()):
                try:
                    shutil.rmtree(extract_to)
                except:
                    pass
            raise e
    
    @staticmethod
    def cleanup_temp_directory(temp_dir):
        """
        清理臨時目錄
        
        Args:
            temp_dir: 臨時目錄路徑
            
        Returns:
            bool: 是否成功清理
        """
        try:
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            return True
        except:
            return False
    
    @staticmethod
    def get_directory_size(directory):
        """
        計算目錄大小
        
        Args:
            directory: 目錄路徑
            
        Returns:
            int: 目錄大小（bytes）
        """
        total_size = 0
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        pass
        except:
            pass
        return total_size
    
    @staticmethod
    def copy_file(src_path, dst_path):
        """
        複製檔案
        
        Args:
            src_path: 來源檔案路徑
            dst_path: 目標檔案路徑
            
        Returns:
            bool: 是否成功複製
        """
        try:
            # 確保目標目錄存在
            dst_dir = os.path.dirname(dst_path)
            FileUtils.ensure_directory_exists(dst_dir)
            
            shutil.copy2(src_path, dst_path)
            return True
        except:
            return False
    
    @staticmethod
    def move_file(src_path, dst_path):
        """
        移動檔案
        
        Args:
            src_path: 來源檔案路徑
            dst_path: 目標檔案路徑
            
        Returns:
            bool: 是否成功移動
        """
        try:
            # 確保目標目錄存在
            dst_dir = os.path.dirname(dst_path)
            FileUtils.ensure_directory_exists(dst_dir)
            
            shutil.move(src_path, dst_path)
            return True
        except:
            return False
    
    @staticmethod
    def delete_file(file_path):
        """
        刪除檔案
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            bool: 是否成功刪除
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except:
            return False
    
    @staticmethod
    def generate_timestamp_filename(base_name, extension=None):
        """
        生成帶時間戳的檔案名稱
        
        Args:
            base_name: 基礎檔案名稱
            extension: 檔案副檔名（如 '.txt'）
            
        Returns:
            str: 帶時間戳的檔案名稱
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if extension:
            return f"{base_name}_{timestamp}{extension}"
        else:
            return f"{base_name}_{timestamp}"
    
    @staticmethod
    def sanitize_filename(filename):
        """
        清理檔案名稱，移除不安全的字符
        
        Args:
            filename: 原始檔案名稱
            
        Returns:
            str: 清理後的檔案名稱
        """
        # 移除或替換不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        clean_filename = filename
        
        for char in unsafe_chars:
            clean_filename = clean_filename.replace(char, '_')
        
        # 移除多餘的空格和點
        clean_filename = clean_filename.strip(' .')
        
        # 限制檔案名稱長度
        max_length = 200
        if len(clean_filename) > max_length:
            name, ext = os.path.splitext(clean_filename)
            clean_filename = name[:max_length-len(ext)] + ext
        
        return clean_filename
    
    @staticmethod
    def get_relative_path(file_path, base_path):
        """
        取得相對路徑
        
        Args:
            file_path: 檔案路徑
            base_path: 基礎路徑
            
        Returns:
            str: 相對路徑
        """
        try:
            return os.path.relpath(file_path, base_path)
        except:
            return file_path
    
    @staticmethod
    def is_path_accessible(path):
        """
        檢查路徑是否可存取
        
        Args:
            path: 路徑
            
        Returns:
            bool: 是否可存取
        """
        try:
            return os.path.exists(path) and os.access(path, os.R_OK)
        except:
            return False


class PathUtils:
    """路徑處理工具類"""
    
    @staticmethod
    def normalize_path(path):
        """
        標準化路徑
        
        Args:
            path: 原始路徑
            
        Returns:
            str: 標準化後的路徑
        """
        return os.path.normpath(os.path.abspath(path))
    
    @staticmethod
    def join_paths(*paths):
        """
        安全地拼接路徑
        
        Args:
            *paths: 路徑組件
            
        Returns:
            str: 拼接後的路徑
        """
        return os.path.join(*paths)
    
    @staticmethod
    def get_parent_directory(path):
        """
        取得父目錄
        
        Args:
            path: 路徑
            
        Returns:
            str: 父目錄路徑
        """
        return os.path.dirname(path)
    
    @staticmethod
    def get_filename(path):
        """
        取得檔案名稱（不含路徑）
        
        Args:
            path: 檔案路徑
            
        Returns:
            str: 檔案名稱
        """
        return os.path.basename(path)
    
    @staticmethod
    def get_filename_without_extension(path):
        """
        取得檔案名稱（不含副檔名）
        
        Args:
            path: 檔案路徑
            
        Returns:
            str: 檔案名稱（不含副檔名）
        """
        filename = os.path.basename(path)
        return os.path.splitext(filename)[0]
    
    @staticmethod
    def is_absolute_path(path):
        """
        檢查是否為絕對路徑
        
        Args:
            path: 路徑
            
        Returns:
            bool: 是否為絕對路徑
        """
        return os.path.isabs(path)
    
    @staticmethod
    def convert_to_absolute_path(path, base_path=None):
        """
        轉換為絕對路徑
        
        Args:
            path: 路徑
            base_path: 基礎路徑（預設為當前工作目錄）
            
        Returns:
            str: 絕對路徑
        """
        if os.path.isabs(path):
            return path
        
        if base_path:
            return os.path.abspath(os.path.join(base_path, path))
        else:
            return os.path.abspath(path)


# ===================== 使用範例 =====================

def example_usage():
    """使用範例"""
    # 檔案工具範例
    print("FileUtils 使用範例:")
    
    # 檢查檔案格式
    # is_supported = FileUtils.is_supported_file("test.csv")
    # print(f"是否支援: {is_supported}")
    
    # 搜尋檔案
    # csv_files = FileUtils.find_files_by_pattern("/path/to/directory", "*.csv")
    # print(f"找到的CSV檔案: {csv_files}")
    
    # 路徑工具範例
    print("PathUtils 使用範例:")
    
    # 標準化路徑
    # normalized = PathUtils.normalize_path("../test/../data/file.csv")
    # print(f"標準化路徑: {normalized}")
    
    print("檔案工具模組已準備就緒")


if __name__ == "__main__":
    example_usage()