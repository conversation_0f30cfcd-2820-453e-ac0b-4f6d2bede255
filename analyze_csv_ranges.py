#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析CSV檔案的良品範圍定位
"""

import csv
import os
import sys

# 添加專案路徑
sys.path.append('.')
from src.core.test_data_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def analyze_csv_ranges():
    """分析CSV檔案的良品範圍"""
    
    # 檔案路徑
    csv_file = "../M8321U31U(CC)_GK4F32.1U_PGM25060020_FT1_R0_ALL_20250625064905.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ 檔案不存在: {csv_file}")
        return
    
    print(f"📁 分析檔案: {os.path.basename(csv_file)}")
    print("=" * 80)
    
    # 創建檢查器
    checker = TestDataChecker()
    
    # 讀取CSV資料
    print("📖 正在讀取CSV檔案...")
    data = checker.read_csv_file(csv_file)
    print(f"✅ 已讀取 {len(data)} 行資料")
    
    # 1. 找到[Data]區段
    print("\n🔍 步驟1: 尋找[Data]區段...")
    data_section_row = checker.find_data_section(data)
    if data_section_row is None:
        print("❌ 找不到[Data]區段")
        return
    print(f"✅ 找到[Data]區段在第 {data_section_row + 1} 行")
    
    # 2. 找到SW_Bin欄位
    print("\n🔍 步驟2: 尋找SW_Bin欄位...")
    sw_bin_row, sw_bin_col = checker.find_sw_bin_column(data, data_section_row)
    if sw_bin_row is None:
        print("❌ 找不到SW_Bin欄位")
        return
    print(f"✅ 找到SW_Bin欄位在第 {sw_bin_row + 1} 行，第 {sw_bin_col + 1} 列 ({checker.col_index_to_excel(sw_bin_col)}欄)")
    
    # 3. 確定資料範圍
    print("\n🔍 步驟3: 確定良品資料範圍...")
    
    # 開始行：SW_Bin往下4格
    data_start_row = sw_bin_row + 4
    print(f"📍 資料開始行: 第 {data_start_row + 1} 行 (SW_Bin行 + 4)")
    
    # 結束行：找[QAData]區段
    qa_data_row = checker.find_qa_data_section(data)
    if qa_data_row is not None:
        data_end_row = qa_data_row
        print(f"📍 資料結束行: 第 {data_end_row} 行 ([QAData]區段前)")
    else:
        data_end_row = len(data)
        print(f"📍 資料結束行: 第 {data_end_row} 行 (檔案末尾)")
    
    print(f"📊 良品資料範圍: 第 {data_start_row + 1} 行 到 第 {data_end_row} 行")
    
    # 4. 分析良品資料
    print("\n🔍 步驟4: 分析良品資料...")
    good_products = []
    
    for row_idx in range(data_start_row, data_end_row):
        if row_idx >= len(data):
            break
            
        sw_bin_value = data[row_idx][sw_bin_col] if sw_bin_col < len(data[row_idx]) else ""
        
        # 如果SW_Bin為1或2（良品）
        if sw_bin_value and str(sw_bin_value).strip() in ['1', '2']:
            current_row = data[row_idx]
            
            # 找到最後一個非空格子
            last_non_empty_col = None
            for col_idx in range(len(current_row) - 1, -1, -1):
                cell_value = current_row[col_idx] if col_idx < len(current_row) else ""
                if cell_value and str(cell_value).strip() != '':
                    last_non_empty_col = col_idx
                    break
            
            good_products.append({
                'row_idx': row_idx,
                'excel_row': row_idx + 1,
                'sw_bin': sw_bin_value,
                'last_non_empty_col': last_non_empty_col,
                'last_non_empty_excel_col': checker.col_index_to_excel(last_non_empty_col) if last_non_empty_col is not None else None
            })
    
    print(f"✅ 找到 {len(good_products)} 個良品")
    
    # 5. 顯示前10個良品的詳細資訊
    print("\n📋 前10個良品的範圍資訊:")
    print("-" * 80)
    for i, product in enumerate(good_products[:10]):
        row_idx = product['row_idx']
        excel_row = product['excel_row']
        sw_bin = product['sw_bin']
        last_col = product['last_non_empty_col']
        last_excel_col = product['last_non_empty_excel_col']
        
        print(f"良品 {i+1}:")
        print(f"  📍 位置: 第 {excel_row} 行")
        print(f"  🏷️  SW_Bin: {sw_bin}")
        print(f"  📊 最後非空欄位: {last_excel_col}欄 (索引 {last_col})")
        
        # 檢查左邊一個空格的下面一個儲存格
        if last_col is not None and last_col > 0:
            left_empty_col = last_col + 1  # 右邊一個位置（空格）
            if row_idx + 1 < len(data):  # 下面一行
                below_row_idx = row_idx + 1
                if left_empty_col < len(data[below_row_idx]):
                    below_cell_value = data[below_row_idx][left_empty_col]
                    below_excel_pos = f"{checker.col_index_to_excel(left_empty_col)}{below_row_idx + 1}"
                    print(f"  🔍 右邊空格下方儲存格 {below_excel_pos}: '{below_cell_value}'")
                else:
                    print(f"  🔍 右邊空格下方儲存格: 超出範圍")
            else:
                print(f"  🔍 右邊空格下方儲存格: 超出檔案範圍")
        
        print()

if __name__ == "__main__":
    analyze_csv_ranges()
