#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試整合後的新CSV邏輯
驗證程式能夠自動選擇正確的邏輯處理不同格式的CSV檔案
"""

import csv
import os
import tempfile
import zipfile
from src.core.test_data_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def create_test_csv_files():
    """創建測試用的CSV檔案"""
    temp_dir = tempfile.mkdtemp()
    
    # 1. 新邏輯格式（E欄位Bin#）
    new_logic_csv = os.path.join(temp_dir, "new_logic_test.csv")
    new_logic_data = [
        ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"],
        ["", "", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "Bin#", "Test1", "Test2", "Test3", "Test4", "Status", ""],
        ["", "", "", "", "2", "100", "200", "300", "400", "NA.", ""],
        ["", "", "", "", "3", "150", "250", "350", "450", "123", ""],
        ["", "", "", "", "5", "180", "280", "380", "480", "NA", ""],
        ["", "", "", "", "1", "120", "220", "320", "420", "NA.", ""],
        ["", "", "", "", "4", "140", "240", "340", "440", "OK", ""],
        ["", "", "", "", "", "", "", "", "", "", ""],
    ]
    
    with open(new_logic_csv, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(new_logic_data)
    
    # 2. 原有邏輯格式（[Data] + SW_Bin）
    old_logic_csv = os.path.join(temp_dir, "old_logic_test.csv")
    old_logic_data = [
        ["Header1", "Header2", "Header3"],
        ["", "", ""],
        ["[Data]", "", ""],
        ["", "SW_Bin", "Test1", "Test2"],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["Product1", "1", "100", "200"],
        ["Product2", "2", "150", ""],
        ["Product3", "5", "180", "280"],
        ["Product4", "1", "", "220"],
        ["", "", "", ""],
    ]
    
    with open(old_logic_csv, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(old_logic_data)
    
    # 3. 創建ZIP檔案（新邏輯格式）
    new_logic_zip = os.path.join(temp_dir, "new_logic_test.csv.zip")
    with zipfile.ZipFile(new_logic_zip, 'w') as zf:
        zf.write(new_logic_csv, "new_logic_test.csv")
    
    # 4. 創建ZIP檔案（原有邏輯格式）
    old_logic_zip = os.path.join(temp_dir, "old_logic_test.csv.zip")
    with zipfile.ZipFile(old_logic_zip, 'w') as zf:
        zf.write(old_logic_csv, "old_logic_test.csv")
    
    return {
        'temp_dir': temp_dir,
        'new_logic_csv': new_logic_csv,
        'old_logic_csv': old_logic_csv,
        'new_logic_zip': new_logic_zip,
        'old_logic_zip': old_logic_zip
    }


def test_integrated_logic():
    """測試整合後的邏輯"""
    print("🧪 開始測試整合後的新CSV邏輯...")
    
    # 創建測試檔案
    test_files = create_test_csv_files()
    
    # 初始化檢查器
    checker = TestDataChecker()
    
    def progress_callback(msg):
        print(f"📝 {msg}")
    
    print("\n" + "="*60)
    print("測試1: 新邏輯格式CSV檔案（E欄位Bin#）")
    print("="*60)
    
    result1 = checker.check_file(test_files['new_logic_csv'], progress_callback)
    print(f"\n📊 結果1:")
    print(f"  檔案: {result1['file']}")
    print(f"  狀態: {result1['status']}")
    print(f"  訊息: {result1['message']}")
    print(f"  邏輯類型: {result1.get('logic_type', '未知')}")
    print(f"  良品數量: {result1.get('total_good_products', 0)}")
    print(f"  異常數量: {len(result1['issues'])}")
    
    if result1['issues']:
        print(f"  發現的異常:")
        for i, issue in enumerate(result1['issues'], 1):
            print(f"    {i}. {issue['description']}")
    
    print("\n" + "="*60)
    print("測試2: 原有邏輯格式CSV檔案（[Data] + SW_Bin）")
    print("="*60)
    
    result2 = checker.check_file(test_files['old_logic_csv'], progress_callback)
    print(f"\n📊 結果2:")
    print(f"  檔案: {result2['file']}")
    print(f"  狀態: {result2['status']}")
    print(f"  訊息: {result2['message']}")
    print(f"  邏輯類型: {result2.get('logic_type', '未知')}")
    print(f"  良品數量: {result2.get('total_good_products', 0)}")
    print(f"  異常數量: {len(result2['issues'])}")
    
    if result2['issues']:
        print(f"  發現的異常:")
        for i, issue in enumerate(result2['issues'], 1):
            print(f"    {i}. {issue['description']}")
    
    print("\n" + "="*60)
    print("測試3: 新邏輯格式ZIP檔案")
    print("="*60)
    
    result3 = checker.extract_and_check_zip_file(test_files['new_logic_zip'], progress_callback)
    print(f"\n📊 結果3:")
    print(f"  檔案: {result3['file']}")
    print(f"  狀態: {result3['status']}")
    print(f"  訊息: {result3['message']}")
    print(f"  邏輯類型: {result3.get('logic_type', '未知')}")
    print(f"  良品數量: {result3.get('total_good_products', 0)}")
    print(f"  異常數量: {len(result3['issues'])}")
    
    print("\n" + "="*60)
    print("測試4: 原有邏輯格式ZIP檔案")
    print("="*60)
    
    result4 = checker.extract_and_check_zip_file(test_files['old_logic_zip'], progress_callback)
    print(f"\n📊 結果4:")
    print(f"  檔案: {result4['file']}")
    print(f"  狀態: {result4['status']}")
    print(f"  訊息: {result4['message']}")
    print(f"  邏輯類型: {result4.get('logic_type', '未知')}")
    print(f"  良品數量: {result4.get('total_good_products', 0)}")
    print(f"  異常數量: {len(result4['issues'])}")
    
    print("\n" + "="*60)
    print("測試5: 報告生成")
    print("="*60)
    
    # 測試報告生成
    all_results = [result1, result2, result3, result4]
    file_paths = [
        test_files['new_logic_csv'],
        test_files['old_logic_csv'], 
        test_files['new_logic_zip'],
        test_files['old_logic_zip']
    ]
    
    txt_path, csv_path = checker.save_results(all_results, file_paths, test_files['temp_dir'])
    print(f"📄 TXT報告: {txt_path}")
    print(f"📄 CSV報告: {csv_path}")
    
    # 檢查報告內容
    if os.path.exists(txt_path):
        print(f"\n✅ TXT報告已生成，大小: {os.path.getsize(txt_path)} bytes")
    if os.path.exists(csv_path):
        print(f"✅ CSV報告已生成，大小: {os.path.getsize(csv_path)} bytes")
    
    print("\n" + "="*60)
    print("🏆 測試總結")
    print("="*60)
    
    # 驗證結果
    success_count = 0
    expected_results = [
        ("新邏輯CSV", result1, 'new_e_column_bin', 4, 2),  # 4個良品，2個異常
        ("原有邏輯CSV", result2, 'original_data_section', 3, 1),  # 3個良品，1個異常
        ("新邏輯ZIP", result3, 'new_e_column_bin', 4, 2),  # 4個良品，2個異常
        ("原有邏輯ZIP", result4, 'original_data_section', 3, 1),  # 3個良品，1個異常
    ]
    
    for name, result, expected_logic, expected_good, expected_issues in expected_results:
        actual_logic = result.get('logic_type', '')
        actual_good = result.get('total_good_products', 0)
        actual_issues = len(result['issues'])
        
        if (actual_logic == expected_logic and 
            actual_good == expected_good and 
            actual_issues == expected_issues):
            print(f"✅ {name}: 通過")
            success_count += 1
        else:
            print(f"❌ {name}: 失敗")
            print(f"   預期: 邏輯={expected_logic}, 良品={expected_good}, 異常={expected_issues}")
            print(f"   實際: 邏輯={actual_logic}, 良品={actual_good}, 異常={actual_issues}")
    
    print(f"\n🎯 測試結果: {success_count}/4 通過")
    
    if success_count == 4:
        print("🎉 所有測試通過！新邏輯整合成功！")
    else:
        print("⚠️  部分測試失敗，需要檢查程式邏輯")
    
    # 清理測試檔案
    import shutil
    shutil.rmtree(test_files['temp_dir'])
    print(f"\n🧹 已清理測試檔案")
    
    return success_count == 4


if __name__ == "__main__":
    test_integrated_logic()