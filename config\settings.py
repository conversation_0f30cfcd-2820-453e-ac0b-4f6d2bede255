#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings and Configuration Module
設定與配置模組
包含程式的所有配置項目
"""

import os
from datetime import datetime, timedelta


class AppConfig:
    """應用程式配置類"""
    
    # ===================== 基本資訊 =====================
    APP_NAME = "測試機資料檢查工具"
    APP_VERSION = "2.0.0"
    APP_DESCRIPTION = "檢查良品（SW_Bin為1或2）是否有缺少資料的問題"
    
    # ===================== 檔案格式配置 =====================
    SUPPORTED_FILE_EXTENSIONS = ['.csv', '.csv.zip', '.spd.zip']
    EXCEL_EXTENSIONS = ['.xlsx', '.xls']
    
    # CSV檔案編碼
    CSV_ENCODING = 'utf-8-sig'
    CSV_OUTPUT_ENCODING = 'utf-8-sig'
    
    # ===================== 網路路徑配置 =====================
    DEFAULT_NETWORK_PATH = r"\\************\test_log"
    
    # 要跳過的路徑（一般搜尋）
    GENERAL_SKIP_PATHS = {
        'ASECL', 'ASEKR', 'ASEKS', 'Testserv', 'Truetest'
    }
    
    # FCST專用跳過路徑（比一般搜尋多一些）
    FCST_SKIP_PATHS = {
        'ASECL', 'ASEKR', 'ASEKS', 'Testserv', 'Truetest',
        'temp', 'backup', 'archive', 'old'
    }
    
    # 測試類型
    TEST_TYPES = ['FT', 'CP']
    
    # ===================== 資料處理配置 =====================
    # 良品SW_Bin值
    GOOD_PRODUCT_BIN_VALUES = ['1', '2']
    
    # 良品Bin#值（SPD格式）
    GOOD_PRODUCT_SPD_BIN_VALUES = [1, 2]
    
    # 資料區段標識
    DATA_SECTION_MARKER = '[Data]'
    QA_DATA_SECTION_MARKER = '[QAData]'
    
    # SW_Bin欄位名稱
    SW_BIN_COLUMN_NAME = 'SW_Bin'
    SPD_BIN_COLUMN_NAME = 'Bin#'
    
    # FCST檔案中的產品編號欄位
    FCST_PART_NO_MARKER = 'GMT Part No'
    
    # ===================== GUI配置 =====================
    # 視窗設定
    MAIN_WINDOW_TITLE = f"{APP_NAME} v{APP_VERSION}"
    MAIN_WINDOW_SIZE = "1200x800"
    MAIN_WINDOW_MIN_SIZE = (1000, 700)
    
    # 進度更新頻率（避免GUI卡頓）
    PROGRESS_UPDATE_INTERVAL = 50  # 每50個項目更新一次進度
    
    # 檔案列表顯示數量限制
    MAX_FILES_DISPLAY = 100
    
    # ===================== 報告配置 =====================
    # 輸出目錄
    DEFAULT_OUTPUT_DIR = "CheckReport"
    
    # 報告檔案格式
    REPORT_FORMATS = ['txt', 'csv']
    
    # 時間戳格式
    TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
    DISPLAY_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    DISPLAY_DATE_FORMAT = "%Y-%m-%d"
    
    # 報告檔案名稱格式
    SINGLE_FILE_REPORT_FORMAT = "{prefix}_data_check_issues_{timestamp}"
    MULTI_FILE_REPORT_FORMAT = "多產品分析_data_check_issues_{timestamp}"
    FCST_REPORT_FORMAT = "FCST_{test_type}_data_check_issues_{timestamp}"
    FULL_SEARCH_REPORT_FORMAT = "全產品搜尋_{test_type}_data_check_issues_{timestamp}"
    
    # ===================== 效能配置 =====================
    # 多線程設定
    MAX_WORKER_THREADS = 4
    
    # 檔案處理批次大小
    FILE_BATCH_SIZE = 10
    
    # 記憶體限制（MB）
    MAX_MEMORY_USAGE_MB = 500
    
    # ===================== 日誌配置 =====================
    # 日誌等級
    LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    
    # 日誌檔案
    LOG_FILE = "app.log"
    LOG_MAX_SIZE_MB = 10
    LOG_BACKUP_COUNT = 5
    
    # ===================== 時間配置 =====================
    # 預設時間區間（天）
    DEFAULT_TIME_RANGE_DAYS = 7
    
    @classmethod
    def get_default_start_date(cls):
        """取得預設開始日期"""
        return datetime.now() - timedelta(days=cls.DEFAULT_TIME_RANGE_DAYS)
    
    @classmethod
    def get_default_end_date(cls):
        """取得預設結束日期"""
        return datetime.now()
    
    # ===================== 錯誤訊息配置 =====================
    ERROR_MESSAGES = {
        'file_not_found': '檔案不存在',
        'file_read_error': '檔案讀取錯誤',
        'invalid_format': '檔案格式不正確',
        'network_error': '網路路徑無法存取',
        'no_data_section': '找不到[Data]區段',
        'no_sw_bin_column': '找不到SW_Bin欄位',
        'no_bin_column': '找不到Bin#欄位',
        'fcst_file_error': 'FCST檔案錯誤',
        'fcst_no_part_no': '找不到GMT Part No欄位',
        'zip_extract_error': 'ZIP檔案解壓縮失敗',
        'report_save_error': '報告保存失敗'
    }
    
    # 成功訊息
    SUCCESS_MESSAGES = {
        'file_processed': '檔案處理完成',
        'report_saved': '報告已保存',
        'analysis_complete': '分析完成',
        'fcst_import_complete': 'FCST匯入完成',
        'full_search_complete': '全產品搜尋完成'
    }
    
    # ===================== 路徑配置 =====================
    @classmethod
    def get_app_dir(cls):
        """取得應用程式目錄"""
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    @classmethod
    def get_output_dir(cls):
        """取得輸出目錄的完整路徑"""
        return os.path.join(cls.get_app_dir(), cls.DEFAULT_OUTPUT_DIR)
    
    @classmethod
    def get_config_dir(cls):
        """取得配置目錄"""
        return os.path.join(cls.get_app_dir(), 'config')
    
    @classmethod
    def get_log_file_path(cls):
        """取得日誌檔案路徑"""
        return os.path.join(cls.get_app_dir(), cls.LOG_FILE)
    
    # ===================== 驗證方法 =====================
    @classmethod
    def is_supported_file(cls, file_path):
        """檢查檔案是否為支援格式"""
        file_ext = os.path.splitext(file_path.lower())[1]
        return file_ext in cls.SUPPORTED_FILE_EXTENSIONS
    
    @classmethod
    def is_excel_file(cls, file_path):
        """檢查是否為Excel檔案"""
        file_ext = os.path.splitext(file_path.lower())[1]
        return file_ext in cls.EXCEL_EXTENSIONS
    
    @classmethod
    def validate_network_path(cls, path):
        """驗證網路路徑"""
        try:
            return os.path.exists(path) and os.path.isdir(path)
        except:
            return False


class UIConfig:
    """UI配置類"""
    
    # ===================== 顏色配置 =====================
    COLORS = {
        'primary': '#2E86C1',
        'secondary': '#85C1E9',
        'success': '#28B463',
        'warning': '#F39C12',
        'error': '#E74C3C',
        'info': '#5DADE2',
        'background': '#FFFFFF',
        'text': '#2C3E50',
        'border': '#BDC3C7'
    }
    
    # ===================== 字體配置 =====================
    FONTS = {
        'default': ('Microsoft YaHei UI', 10),
        'title': ('Microsoft YaHei UI', 14, 'bold'),
        'header': ('Microsoft YaHei UI', 12, 'bold'),
        'small': ('Microsoft YaHei UI', 9),
        'monospace': ('Consolas', 10)
    }
    
    # ===================== 間距配置 =====================
    PADDING = {
        'small': 5,
        'medium': 10,
        'large': 15,
        'xlarge': 20
    }
    
    # ===================== 按鈕配置 =====================
    BUTTON_STYLES = {
        'primary': {
            'bg': '#2E86C1',
            'fg': 'white',
            'activebackground': '#1B4F72',
            'activeforeground': 'white'
        },
        'secondary': {
            'bg': '#85C1E9',
            'fg': '#2C3E50',
            'activebackground': '#5DADE2',
            'activeforeground': '#2C3E50'
        },
        'success': {
            'bg': '#28B463',
            'fg': 'white',
            'activebackground': '#1E8449',
            'activeforeground': 'white'
        },
        'warning': {
            'bg': '#F39C12',
            'fg': 'white',
            'activebackground': '#D68910',
            'activeforeground': 'white'
        },
        'danger': {
            'bg': '#E74C3C',
            'fg': 'white',
            'activebackground': '#C0392B',
            'activeforeground': 'white'
        }
    }


class DevConfig(AppConfig):
    """開發環境配置"""
    LOG_LEVEL = "DEBUG"
    MAX_WORKER_THREADS = 2
    PROGRESS_UPDATE_INTERVAL = 10


class ProdConfig(AppConfig):
    """生產環境配置"""
    LOG_LEVEL = "WARNING"
    MAX_WORKER_THREADS = 8
    PROGRESS_UPDATE_INTERVAL = 100


# ===================== 配置選擇 =====================
def get_config():
    """根據環境變數選擇配置"""
    env = os.getenv('APP_ENV', 'prod').lower()
    
    if env == 'dev':
        return DevConfig()
    else:
        return ProdConfig()


# 預設配置實例
config = get_config()


# ===================== 使用範例 =====================
def example_usage():
    """使用範例"""
    print(f"應用程式名稱: {config.APP_NAME}")
    print(f"版本: {config.APP_VERSION}")
    print(f"預設網路路徑: {config.DEFAULT_NETWORK_PATH}")
    print(f"支援的檔案格式: {config.SUPPORTED_FILE_EXTENSIONS}")
    print(f"輸出目錄: {config.get_output_dir()}")
    print("配置模組已準備就緒")


if __name__ == "__main__":
    example_usage()