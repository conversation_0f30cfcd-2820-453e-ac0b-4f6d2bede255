#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新CSV邏輯測試程式
測試E欄位Bin#格式的檢查邏輯
"""

import csv
import os
from datetime import datetime


class NewCSVLogicChecker:
    """新CSV邏輯檢查器"""
    
    def __init__(self):
        self.results = []
    
    def find_bin_column_in_e(self, data):
        """在E欄位往下找Bin#"""
        e_col_index = 4  # E欄位是索引4 (A=0, B=1, C=2, D=3, E=4)
        
        for row_idx, row in enumerate(data):
            if len(row) > e_col_index:
                cell_value = str(row[e_col_index]).strip()
                if cell_value == "Bin#":
                    print(f"✅ 在 E{row_idx+1} 找到 Bin#")
                    return row_idx, e_col_index
        
        print("❌ 在E欄位中找不到 Bin#")
        return None, None
    
    def find_width_boundary(self, data, bin_row):
        """找到截止範圍(寬度) - 從Bin#往右找到空格的左邊一格"""
        if bin_row >= len(data):
            return None
        
        row_data = data[bin_row]
        
        # 從E欄位往右搜尋
        for col_idx in range(4, len(row_data)):  # 從E欄位(索引4)開始
            cell_value = str(row_data[col_idx]).strip() if col_idx < len(row_data) else ""
            
            # 找到空格
            if cell_value == "":
                width_boundary = col_idx - 1  # 空格的左邊一格
                print(f"✅ 截止範圍(寬度): {self.col_index_to_excel(width_boundary)}{bin_row+1}")
                return width_boundary
        
        # 如果沒找到空格，使用最後一個有資料的欄位
        width_boundary = len(row_data) - 1
        print(f"✅ 截止範圍(寬度): {self.col_index_to_excel(width_boundary)}{bin_row+1} (檔案末尾)")
        return width_boundary
    
    def find_depth_boundary(self, data, bin_row, bin_col):
        """找到截止範圍(深度) - 從Bin#往下找到空格的上一格"""
        
        # 從Bin#的下一行開始往下搜尋
        for row_idx in range(bin_row + 1, len(data)):
            if bin_col >= len(data[row_idx]):
                depth_boundary = row_idx - 1
                print(f"✅ 截止範圍(深度): E{depth_boundary+1} (資料不足)")
                return depth_boundary
            
            cell_value = str(data[row_idx][bin_col]).strip()
            
            # 找到空格
            if cell_value == "":
                depth_boundary = row_idx - 1  # 空格的上一格
                print(f"✅ 截止範圍(深度): E{depth_boundary+1}")
                return depth_boundary
        
        # 如果沒找到空格，使用檔案末尾
        depth_boundary = len(data) - 1
        print(f"✅ 截止範圍(深度): E{depth_boundary+1} (檔案末尾)")
        return depth_boundary
    
    def check_new_csv_logic(self, data, progress_callback=None):
        """執行新的CSV檢查邏輯"""
        issues = []
        
        if progress_callback:
            progress_callback("開始新CSV邏輯檢查...")
        
        # 1. 找到E欄位的Bin#
        bin_row, bin_col = self.find_bin_column_in_e(data)
        if bin_row is None:
            return issues, 0  # 沒找到Bin#，不適用新邏輯
        
        # 2. 找到截止範圍(寬度)
        width_boundary = self.find_width_boundary(data, bin_row)
        if width_boundary is None:
            return issues, 0
        
        # 3. 找到截止範圍(深度)  
        depth_boundary = self.find_depth_boundary(data, bin_row, bin_col)
        if depth_boundary is None:
            return issues, 0
        
        print(f"📊 分析範圍: E{bin_row+2}:{self.col_index_to_excel(width_boundary)}{depth_boundary+1}")
        
        # 4. 檢查每一行的資料
        good_products_count = 0
        
        for row_idx in range(bin_row + 1, depth_boundary + 1):
            if row_idx >= len(data):
                break
                
            row_data = data[row_idx]
            
            # 取得E欄位的Bin值
            if bin_col >= len(row_data):
                continue
            
            try:
                bin_value = float(str(row_data[bin_col]).strip())
            except (ValueError, IndexError):
                continue  # 跳過無效的Bin值
            
            # 取得右邊界欄位的值
            if width_boundary >= len(row_data):
                right_boundary_value = ""
            else:
                right_boundary_value = str(row_data[width_boundary]).strip()
            
            # 判斷邏輯：良品(≤4)且右邊界以NA開頭 → 異常
            if bin_value <= 4:
                good_products_count += 1
                
                # 除錯訊息
                print(f"🔍 檢查 E{row_idx+1}={bin_value} (良品), {self.col_index_to_excel(width_boundary)}{row_idx+1}='{right_boundary_value}'")
                
                if str(right_boundary_value).upper().startswith("NA"):
                    issues.append({
                        'row': row_idx + 1,  # Excel行號
                        'bin_value': bin_value,
                        'right_boundary_col': self.col_index_to_excel(width_boundary),
                        'right_boundary_value': right_boundary_value,
                        'issue': f"良品(Bin={bin_value})在{self.col_index_to_excel(width_boundary)}{row_idx+1}位置測試資料異常",
                        'description': f"E{row_idx+1}={bin_value} (良品), {self.col_index_to_excel(width_boundary)}{row_idx+1}={right_boundary_value} (異常)"
                    })
                    
                    if progress_callback:
                        progress_callback(f"❌ 發現異常: E{row_idx+1}={bin_value}, {self.col_index_to_excel(width_boundary)}{row_idx+1}={right_boundary_value}")
        
        if progress_callback:
            progress_callback(f"✅ 新邏輯檢查完成: 良品數量={good_products_count}, 異常數量={len(issues)}")
        
        return issues, good_products_count
    
    def col_index_to_excel(self, col_idx):
        """將列索引轉換為Excel格式"""
        result = ""
        while col_idx >= 0:
            result = chr(col_idx % 26 + ord('A')) + result
            col_idx = col_idx // 26 - 1
        return result
    
    def test_with_sample_data(self):
        """使用範例資料測試"""
        print("🧪 開始測試新CSV邏輯...")
        
        # 創建測試資料
        test_data = [
            # 標題行
            ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"],
            ["", "", "", "", "", "", "", "", "", ""],
            ["", "", "", "", "", "", "", "", "", ""],
            # Bin#行
            ["", "", "", "", "Bin#", "Test1", "Test2", "Test3", "Test4", "Status", ""],  # 第4行，E4=Bin#，K4為空格
            # 資料行
            ["", "", "", "", "2", "100", "200", "300", "400", "NA.", ""],    # E5=2(良品), J5=NA. → 異常
            ["", "", "", "", "3", "150", "250", "350", "450", "123", ""],    # E6=3(良品), J6=123 → 正常  
            ["", "", "", "", "5", "180", "280", "380", "480", "NA", ""],     # E7=5(不良品), J7=NA → 正常(不檢查)
            ["", "", "", "", "1", "120", "220", "320", "420", "NA.", ""],    # E8=1(良品), J8=NA. → 異常
            ["", "", "", "", "4", "140", "240", "340", "440", "OK", ""],     # E9=4(良品), J9=OK → 正常
            ["", "", "", "", "", "", "", "", "", ""],                    # 空行，作為深度邊界
        ]
        
        def progress_callback(msg):
            print(f"📝 {msg}")
        
        # 執行檢查
        issues, good_products_count = self.check_new_csv_logic(test_data, progress_callback)
        
        # 顯示結果
        print(f"\n📊 檢查結果:")
        print(f"良品總數: {good_products_count}")
        print(f"異常數量: {len(issues)}")
        
        if issues:
            print(f"\n❌ 發現的異常:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue['description']}")
        else:
            print(f"\n✅ 沒有發現異常")
        
        return issues, good_products_count


def main():
    """主程式"""
    checker = NewCSVLogicChecker()
    
    # 執行測試
    issues, good_products = checker.test_with_sample_data()
    
    print(f"\n🎯 預期結果:")
    print(f"  應該找到 2 個異常 (E5和E8的良品資料)")
    print(f"  良品總數應該是 4 個 (Bin值≤4)")
    
    print(f"\n🏆 實際結果:")
    print(f"  找到 {len(issues)} 個異常")
    print(f"  良品總數 {good_products} 個")
    
    if len(issues) == 2 and good_products == 4:
        print(f"\n✅ 測試通過！邏輯正確！")
    else:
        print(f"\n❌ 測試失敗，需要調整邏輯")


if __name__ == "__main__":
    main()