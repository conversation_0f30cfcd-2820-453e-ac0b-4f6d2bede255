# 測試機資料檢查工具 - 重構版本

## 專案簡介

這是一個用於檢查半導體測試機良品資料完整性的工具，主要功能是檢查SW_Bin為1或2的良品是否有缺少測試資料的問題。

## 主要功能

### 1. 檔案檢查
- 支援CSV、CSV.ZIP、SPD.ZIP格式
- 檢查良品（SW_Bin=1或2）的資料完整性
- 自動生成詳細報告

### 2. 資料夾分析
- 批次處理資料夾中的所有支援檔案
- 支援大量檔案的平行處理

### 3. 單一外包廠分析
- 根據時間區間篩選特定外包廠的測試資料
- 支援FT/CP測試類型選擇

### 4. 全產品搜尋
- 搜尋網路路徑下的所有產品測試資料
- 智能過濾和時間範圍選擇

### 5. FCST匯入功能
- 從Excel格式的FCST檔案匯入產品清單
- 自動搜尋對應的測試資料
- 支援產品名稱的精確匹配和括號變化匹配

## 專案結構

```
重構程式/
├── main.py                    # 主程式入口點
├── requirements.txt           # 依賴套件清單
├── README.md                 # 專案說明文件
├── config/                   # 配置目錄
│   └── settings.py           # 應用程式配置
├── src/                      # 源碼目錄
│   ├── core/                 # 核心邏輯模組
│   │   ├── test_data_checker.py    # 資料檢查核心邏輯
│   │   └── fcst_processor.py       # FCST處理核心邏輯
│   ├── gui/                  # GUI模組
│   │   └── main_gui.py       # 主要GUI界面
│   └── utils/                # 工具模組
│       └── file_utils.py     # 檔案處理工具
└── CheckReport/              # 報告輸出目錄
```

## 技術架構

### 模組化設計
- **核心邏輯分離**: 將資料處理邏輯從GUI中分離出來
- **配置統一管理**: 所有設定項目集中在settings.py中
- **工具函數復用**: 通用的檔案操作功能獨立成工具模組
- **清晰的職責分工**: 每個模組都有明確的功能定位

### 主要模組說明

#### 1. 核心模組 (src/core/)
- `TestDataChecker`: 提供資料檢查的核心功能
- `FCSTProcessor`: 處理FCST相關的所有功能

#### 2. GUI模組 (src/gui/)
- `TestDataCheckerGUI`: 主要的圖形界面
- `BaseDialog`: 基礎對話框類
- `TimeTypeDialog`: 時間和類型選擇對話框
- `ProgressManager`: 進度管理器

#### 3. 工具模組 (src/utils/)
- `FileUtils`: 檔案操作工具函數
- `PathUtils`: 路徑處理工具函數

#### 4. 配置模組 (config/)
- `AppConfig`: 應用程式配置
- `UIConfig`: 界面配置
- `DevConfig/ProdConfig`: 環境特定配置

## 安裝與使用

### 系統需求
- Python 3.7+
- Windows作業系統（因需存取網路路徑）

### 安裝步驟

1. 下載或複製專案到本地目錄
2. 安裝依賴套件：
   ```bash
   pip install -r requirements.txt
   ```

### 運行程式

1. 直接運行主程式：
   ```bash
   python main.py
   ```

2. 或者運行GUI模組：
   ```bash
   python src/gui/main_gui.py
   ```

## 使用指南

### 1. 檔案檢查
1. 點擊「選擇檔案」按鈕選擇要檢查的檔案
2. 點擊「開始檢查」進行分析
3. 查看結果並等待報告生成

### 2. 資料夾分析
1. 點擊「選擇資料夾」選擇包含測試檔案的資料夾
2. 點擊「開始檢查」批次處理所有支援的檔案

### 3. 單一外包廠分析
1. 點擊「單一外包廠分析」
2. 選擇外包廠資料夾
3. 設定時間區間和測試類型（FT/CP）
4. 等待分析完成

### 4. 全產品搜尋
1. 點擊「全產品搜尋」
2. 設定時間區間和測試類型
3. 程式會自動搜尋網路路徑下的所有產品

### 5. FCST匯入
1. 點擊「從FCST匯入」
2. 選擇FCST Excel檔案
3. 設定時間區間和測試類型
4. 程式會根據FCST中的產品清單自動搜尋和分析

## 報告格式

程式會生成兩種格式的報告：

### TXT報告
- 詳細的檢查報告
- 包含檔案狀態、問題詳情等
- 適合人工閱讀

### CSV報告
- 結構化的數據報告
- 包含檔案名稱、問題行號、SW_Bin值等
- 適合進一步數據分析

## 配置說明

可以通過修改 `config/settings.py` 來調整程式設定：

- `DEFAULT_NETWORK_PATH`: 預設網路路徑
- `SUPPORTED_FILE_EXTENSIONS`: 支援的檔案格式
- `GOOD_PRODUCT_BIN_VALUES`: 良品的SW_Bin值
- `MAIN_WINDOW_SIZE`: 主視窗大小
- 其他UI和功能相關設定

## 開發說明

### 環境設定
設置環境變數 `APP_ENV=dev` 可以啟用開發模式，會有更詳細的日誌和除錯資訊。

### 程式碼結構
- 所有GUI邏輯在 `src/gui/` 中
- 所有核心邏輯在 `src/core/` 中
- 通用工具函數在 `src/utils/` 中
- 配置文件在 `config/` 中

### 擴展指南
要新增功能，建議：
1. 核心邏輯添加到對應的core模組中
2. GUI界面添加到gui模組中
3. 新的配置項目添加到settings.py中
4. 通用工具函數添加到utils模組中

## 故障排除

### 常見問題

1. **無法存取網路路徑**
   - 檢查網路連接
   - 確認有相應的訪問權限
   - 檢查防火牆設定

2. **檔案讀取錯誤**
   - 確認檔案格式正確
   - 檢查檔案是否被其他程式佔用
   - 確認檔案編碼格式

3. **記憶體不足**
   - 減少同時處理的檔案數量
   - 檢查檔案大小是否過大
   - 關閉其他佔用記憶體的程式

## 版本歷史

### v2.0.0 (重構版本)
- 完全重構程式架構
- 模組化設計，提升可維護性
- 改善使用者界面
- 優化效能和穩定性
- 增強錯誤處理

### v1.0.0 (原始版本)
- 基本檔案檢查功能
- FCST匯入功能
- 全產品搜尋功能

## 授權

本專案為內部工具，僅供GMT公司內部使用。

## 聯絡資訊

如有問題或建議，請聯絡開發團隊。